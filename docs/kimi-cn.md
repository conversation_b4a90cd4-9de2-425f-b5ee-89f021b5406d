如何获取 Kimi 的 refresh_token 和 access_token
============================================

1. 去 [Kimi 官网](https://kimi.moonshot.cn) 登录

2. 根据下面视频教程获取 `refresh_token` 和 `access_token` 填入设置框中，千万别填反了！！！！！！！！！！！！！！

  https://github.com/openai-translator/openai-translator/assets/1206493/bd5d13aa-9469-4075-bbae-f490cd6667cd

  ![settings](https://github.com/openai-translator/openai-translator/assets/1206493/d04a46c5-68c6-44bb-b230-ba8b072fd23d)
