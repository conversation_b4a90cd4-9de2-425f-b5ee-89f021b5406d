{"name": "openai-translator", "version": "0.1.0", "description": "openai-translator", "packageManager": "pnpm@9.1.3", "main": "index.js", "scripts": {"prepare": "pnpm exec simple-git-hooks", "build-tauri-renderer": "tsc && vite build -c vite.config.tauri.ts", "dev-tauri-renderer": "vite -c vite.config.tauri.ts --force", "build-tauri": "npm run build-tauri-renderer && tauri build", "dev-tauri": "tauri dev", "dev-chromium": "vite -c vite.config.chromium.ts", "dev-firefox": "NODE_ENV=development vite build -c vite.config.firefox.ts --watch", "build-browser-extension": "tsc && make build-browser-extension", "build-userscript": "make build-userscript", "clean": "make clean", "test": "vitest test", "test:e2e": "playwright test", "lint": "eslint \"src/**/*.{ts,tsx}\" --cache", "lint:fix": "eslint --fix \"src/**/*.{ts,tsx}\"", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,css,md,json}\" --cache"}, "simple-git-hooks": {"pre-commit": "pnpm exec lint-staged"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint \"src/**/*.{ts,tsx}\" --cache", "prettier --write \"src/**/*.{js,jsx,ts,tsx,css,md,json}\" --cache"]}, "author": "", "license": "AGPL-3.0", "repository": {"type": "git", "url": "https://github.com/openai-translator/openai-translator.git"}, "dependencies": {"@aptabase/tauri": "github:aptabase/tauri-plugin-aptabase#v2", "@floating-ui/dom": "^1.5.1", "@sentry/react": "^7.61.0", "@tauri-apps/api": "2.0.0-beta.9", "@tauri-apps/plugin-autostart": "github:tauri-apps/tauri-plugin-autostart#v2", "@tauri-apps/plugin-fs": "github:tauri-apps/tauri-plugin-fs#v2", "@tauri-apps/plugin-global-shortcut": "github:tauri-apps/tauri-plugin-global-shortcut#13c59ded715e231a17d2ce970710cc339757c4b1", "@tauri-apps/plugin-http": "github:tauri-apps/tauri-plugin-http#v2", "@tauri-apps/plugin-notification": "github:tauri-apps/tauri-plugin-notification#v2", "@tauri-apps/plugin-process": "github:tauri-apps/tauri-plugin-process#v2", "@tauri-apps/plugin-shell": "github:tauri-apps/tauri-plugin-shell#v2", "@tauri-apps/plugin-updater": "github:tauri-apps/tauri-plugin-updater#v2", "@types/color": "^3.0.6", "@types/qs": "^6.9.10", "@types/react-copy-to-clipboard": "^5.0.4", "@types/react-syntax-highlighter": "^15.5.7", "@types/react-window": "^1.8.5", "baseui-sd": "^12.2.4", "best-effort-json-parser": "^1.0.1", "clsx": "^1.2.1", "color": "^4.2.3", "common-tags": "^1.8.2", "date-fns": "^2.29.3", "dayjs": "^1.11.10", "dexie": "^3.2.3", "dexie-react-hooks": "^1.1.3", "electron-store": "^8.1.0", "eventsource-parser": "^1.0.0", "hotkeys-js": "^3.10.1", "i18next": "^23.4.4", "i18next-browser-languagedetector": "^7.1.0", "i18next-http-backend": "^2.2.1", "iso-639-1": "^3.0.1", "jotai": "^2.8.0", "js-sha3": "^0.9.3", "js-tiktoken": "^1.0.10", "jss": "^10.10.0", "jss-preset-default": "^10.10.0", "katex": "^0.16.8", "lodash.debounce": "^4.0.8", "lru-cache": "^10.0.1", "prism-react-renderer": "^2.3.0", "qs": "^6.11.2", "rc-field-form": "^1.36.0", "react": "^18.2.0", "react-code-block": "^1.0.0", "react-copy-to-clipboard": "^5.1.0", "react-country-flag": "^3.1.0", "react-dom": "^18.2.0", "react-draggable": "^4.4.5", "react-dropzone": "^14.2.3", "react-error-boundary": "^4.0.10", "react-ga4": "^2.1.0", "react-hooks-global-state": "^2.1.0", "react-hot-toast": "^2.4.1", "react-hotkeys-hook": "^4.4.1", "react-i18next": "^13.0.3", "react-icons": "^5.0.1", "react-jss": "^10.10.0", "react-latex-next": "^2.2.0", "react-markdown": "^8.0.7", "react-syntax-highlighter": "npm:@fengkx/react-syntax-highlighter@15.6.1", "react-use": "^17.4.2", "react-window": "^1.8.9", "styletron-engine-atomic": "^1.5.0", "styletron-react": "^6.1.0", "swr": "^2.2.0", "tesseract.js": "^4.0.2", "underscore": "^1.13.6", "url-join-ts": "^1.0.5", "use-deep-compare": "^1.1.0", "use-resize-observer": "^9.1.0", "uuid": "^9.0.0", "web-streams-polyfill": "^3.2.1", "zustand": "^4.4.0"}, "devDependencies": {"@playwright/test": "^1.34.3", "@samrum/vite-plugin-web-extension": "^5.0.0", "@tauri-apps/cli": "2.0.0-beta.13", "@types/chrome": "0.0.242", "@types/common-tags": "^1.8.3", "@types/fs-extra": "^11.0.1", "@types/lodash.debounce": "^4.0.7", "@types/node": "^20.5.9", "@types/react": "^18.2.18", "@types/react-dom": "^18.2.7", "@types/tampermonkey": "^4.0.10", "@types/underscore": "^1.11.6", "@types/uuid": "^9.0.2", "@types/webextension-polyfill": "^0.10.1", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.6.0", "@vitejs/plugin-react": "^4.0.4", "archiver": "^5.3.1", "base64-inline-loader": "^2.0.1", "chokidar-cli": "^3.0.0", "electron": "^23.1.3", "electron-util": "0.17.x", "esbuild": "^0.19.2", "esbuild-plugin-copy": "^2.1.1", "esbuild-plugin-inline-import": "^1.0.1", "esbuild-server": "^0.3.0", "eslint": "^8.46.0", "eslint-config-prettier": "^8.9.0", "eslint-import-resolver-typescript": "^3.5.5", "eslint-plugin-baseui": "^13.0.0", "eslint-plugin-import": "^2.28.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.33.1", "eslint-plugin-react-hooks": "^4.6.0", "fs-extra": "^11.1.1", "jsdom": "^22.1.0", "lint-staged": "^15.2.2", "prettier": "^3.0.0", "rollup-plugin-visualizer": "^5.9.0", "simple-git-hooks": "^2.11.1", "typescript": "^5.1.6", "vite": "^4.3.9", "vite-plugin-dynamic-import": "^1.4.1", "vite-plugin-monkey": "^3.5.0", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^4.2.0", "vitest": "^0.34.3", "webextension-polyfill": "^0.10.0"}, "build": {"appId": "xyz.yetone.apps", "productName": "OpenAI Translator", "extraMetadata": {"name": "openai-translator", "main": "main.js"}, "files": [{"from": ".", "filter": ["package.json"]}, {"from": "dist/electron/main"}, {"from": "dist/electron/renderer"}], "win": {"icon": "src/electron/assets/images/icon.png", "target": ["zip"]}, "mac": {"icon": "src/electron/assets/images/icon.png", "target": ["zip"]}, "linux": {"icon": "src/electron/assets/images/icon.png", "target": ["zip"]}, "directories": {"buildResources": "resources"}, "publish": null}}