<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'">

    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">

    <link rel="stylesheet" href="../Style.css">
    <script src="../Script.js" defer></script>
</head>
<body>
    <img src="../Icon.png" width="128" height="128" alt="OpenAI Translator Icon">
    <p class="platform-ios">You can turn on OpenAI Translator’s Safari extension in Settings.</p>
    <p class="platform-mac state-unknown">You can turn on OpenAI Translator’s extension in Safari Extensions preferences.</p>
    <p class="platform-mac state-on">OpenAI Translator’s extension is currently on. You can turn it off in Safari Extensions preferences.</p>
    <p class="platform-mac state-off">OpenAI Translator’s extension is currently off. You can turn it on in Safari Extensions preferences.</p>
    <button class="platform-mac open-preferences">Quit and Open Safari Extensions Preferences…</button>
</body>
</html>
