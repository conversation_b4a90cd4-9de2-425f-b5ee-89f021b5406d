name: Feature
description: Add new feature, improve code, and more
labels: [ "enhancement" ]
body:
  - type: markdown
    attributes:
      value: |
        **非常感谢您的功能建议！Thank you very much for your feature proposal!**
  - type: checkboxes
    attributes:
      label: Search before asking
      description: >
        请在提交前搜索 [issues](https://github.com/yetone/openai-translator/issues)，以查看您的问题是否已经被提交。

        Please search [issues](https://github.com/yetone/openai-translator/issues) to check if your issue has already been reported.
      options:
        - label: >
            在 [issues](https://github.com/yetone/openai-translator/issues) 中没有找到类似的内容。
            
            I searched in the [issues](https://github.com/yetone/openai-translator/issues) and found nothing similar.
          required: true
  - type: input
    attributes:
      label: feature
      description: >
        一句话概括你的功能建议。Please provide a brief description of your feature proposal.
    validations:
      required: true
  - type: textarea
    attributes:
      label: 描述 Motivation
      description: >
        解释一下这个功能将如何解决您的问题。

        Explain how this feature will resolve your problem, including the way it addresses the issue that you are facing.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Solution
      description: 描述建议的解决方案。Describe the proposed solution. (if you have any additional information, please add it here.)
  - type: textarea
    attributes:
      label: 还有其他内容吗？Anything else?
  - type: checkboxes
    attributes:
      label: 你是否愿意提交一份 PR？Are you willing to submit a PR?
      description: >
        我们期待开发人员和用户的帮助，以解决在 OpenAI Translator 中发现的任何问题。 如果您愿意通过提交 PR 来解决此问题，请勾选。We eagerly anticipate developers' and users' support and collaboration in resolving any issues found in OpenAI Translator. If you are willing to offer a solution by submitting a PR to fix this matter, kindly mark the checkbox provided.
      options:
        - label: 我愿意提供 PR! I'm willing to submit a PR!
  - type: markdown
    attributes:
      value: "非常感谢您的功能建议！Thank you very much for your feature proposal!"

