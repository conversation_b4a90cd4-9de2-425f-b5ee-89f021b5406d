name: Test Build

on:
  workflow_dispatch:

jobs:
  build-tauri:
    permissions:
      contents: write
    strategy:
      fail-fast: false
      matrix:
        platform: [macos-latest, ubuntu-20.04, windows-latest]

    runs-on: ${{ matrix.platform }}
    steps:
      - uses: actions/checkout@v4

      - uses: pnpm/action-setup@v2
        with:
          version: 8.6.0

      - uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'pnpm'

      - name: install Rust nightly
        uses: dtolnay/rust-toolchain@nightly

      - name: install dependencies (ubuntu only)
        if: matrix.platform == 'ubuntu-20.04'
        run: |
          sudo apt-get update
          sudo apt-get install -y libgtk-3-dev libwebkit2gtk-4.0-dev libappindicator3-dev librsvg2-dev patchelf libx11-dev libxdo-dev libxcb-shape0-dev libxcb-xfixes0-dev

      - name: install dependencies (mac only)
        if: matrix.platform == 'macos-latest'
        run: |
          rustup target add aarch64-apple-darwin

      - name: install frontend dependencies
        run: pnpm install --no-frozen-lockfile # change this to npm or pnpm depending on which one you use

      - name: Build Tauri App (MacOS Universal)
        uses: tauri-apps/tauri-action@dev
        if: matrix.platform == 'macos-latest'
        id: tauri-action-mac
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          releaseId: test-release
          args: --target universal-apple-darwin

      - name: Build Tauri App
        uses: tauri-apps/tauri-action@dev
        if: matrix.platform != 'macos-latest'
        id: tauri-action
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          releaseId: test-release

      - name: Upload artifact
        uses: actions/upload-artifact@v3
        with:
          name: tauri-client-app-artifact
          path: |
            ${{ fromJSON(steps.tauri-action-mac.outputs.artifactPaths)[0] }}
            ${{ fromJSON(steps.tauri-action.outputs.artifactPaths)[0] }}


  build-browser-extension:
    runs-on: ubuntu-22.04

    steps:
      - uses: actions/checkout@v4

      - uses: pnpm/action-setup@v2
        with:
          version: 8.6.0

      - uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile

      - name: Build
        run: make build-browser-extension
      # todo: upload browser extension artifacts
