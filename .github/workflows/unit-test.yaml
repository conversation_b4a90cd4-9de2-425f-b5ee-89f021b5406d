name: Unit Test

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  unit-test:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - uses: pnpm/action-setup@v2
        with:
          version: 8.6.0

      - uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'pnpm'

      - name: Install packages
        run: pnpm install --no-frozen-lockfile

      - name: Run test
        run: pnpm test
