name: Release

on:
  push:
    tags: [ v\d+\.\d+\.\d+ ]

jobs:
  create-release:
    permissions:
      contents: write
    runs-on: ubuntu-20.04
    outputs:
      release_id: ${{ steps.create-release.outputs.id }}
      release_upload_url: ${{ steps.create-release.outputs.upload_url }}
      release_body: "${{ steps.tag.outputs.message }}"

    steps:
      - uses: actions/checkout@v4

      - name: Get version
        id: get_version
        uses: battila7/get-version-action@v2

      - name: Get tag message
        id: tag
        run: |
          git fetch --depth=1 origin +refs/tags/*:refs/tags/*
          echo "message<<EOF" >> $GITHUB_OUTPUT
          echo "$(git tag -l --format='%(contents)' ${{ steps.get_version.outputs.version }})" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Create Release
        id: create-release
        uses: ncipollo/release-action@v1
        with:
          draft: true
          name: ${{ steps.get_version.outputs.version }}
          tag: ${{ steps.get_version.outputs.version }}
          body: "${{ steps.tag.outputs.message }}"

  build-tauri-renderer:
    needs: create-release
    permissions:
      contents: write
      packages: write
    runs-on: ubuntu-20.04
    steps:
      - uses: actions/checkout@v4

      - name: Get version
        id: get_version
        uses: battila7/get-version-action@v2

      - uses: pnpm/action-setup@v2
        with:
          version: 8.6.0

      - name: setup node
        uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'pnpm'

      - name: install frontend dependencies
        run: pnpm install --no-frozen-lockfile # change this to npm or pnpm depending on which one you use

      - name: Change Version
        env:
          VERSION: "${{ steps.get_version.outputs.version-without-v }}"
        run: make change-version change-package-version

      - uses: oNaiPs/secrets-to-env-action@v1
        with:
          secrets: ${{ toJSON(secrets) }}

      - name: build tauri renderer
        run: pnpm build-tauri-renderer

      - uses: actions/upload-artifact@v3
        with:
          name: tauri-renderer
          path: dist/tauri/

  build-tauri:
    needs: [create-release, build-tauri-renderer]
    permissions:
      contents: write
      packages: write
    strategy:
      fail-fast: false
      matrix:
        config:
          - os: ubuntu-latest
            arch: x86_64
            rust_target: x86_64-unknown-linux-gnu
          - os: macos-13
            arch: x86_64
            rust_target: x86_64-apple-darwin
          - os: macos-latest
            arch: aarch64
            rust_target: aarch64-apple-darwin
          - os: windows-latest
            arch: x86_64
            rust_target: x86_64-pc-windows-msvc

    runs-on: ${{ matrix.config.os }}
    steps:
      - uses: actions/checkout@v4

      - name: Get version
        id: get_version
        uses: battila7/get-version-action@v2

      - uses: pnpm/action-setup@v2
        with:
          version: 8.6.0

      - name: Setup node
        uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'pnpm'

      - name: Install frontend dependencies
        run: pnpm install --no-frozen-lockfile # change this to npm or pnpm depending on which one you use

      - name: Install Rust nightly
        uses: dtolnay/rust-toolchain@nightly
        with:
          targets: ${{ matrix.config.rust_target }}
          toolchain: '1.78.0'

      - name: Install dependencies (ubuntu only)
        if: matrix.config.os == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo apt-get install -y libgtk-3-dev libwebkit2gtk-4.1-dev libappindicator3-dev librsvg2-dev patchelf libx11-dev libxdo-dev libxcb-shape0-dev libxcb-xfixes0-dev
          sudo apt-get install -y libunwind-dev libgstreamer1.0-dev libgstreamer-plugins-base1.0-dev libgstreamer-plugins-bad1.0-dev gstreamer1.0-plugins-base gstreamer1.0-plugins-good gstreamer1.0-plugins-bad gstreamer1.0-plugins-ugly gstreamer1.0-libav gstreamer1.0-tools gstreamer1.0-x gstreamer1.0-alsa gstreamer1.0-gl gstreamer1.0-gtk3 gstreamer1.0-qt5 gstreamer1.0-pulseaudio

      - name: Install dependencies (mac only)
        if: matrix.config.os == 'macos-latest'
        run: |
          rustup target add aarch64-apple-darwin

      - name: Change Version
        env:
          VERSION: "${{ steps.get_version.outputs.version-without-v }}"
        run: make change-version change-package-version

      - uses: actions/download-artifact@v3
        with:
          name: tauri-renderer
          path: dist/tauri

      - name: Install Cargo Tauri for info
        run: cargo install tauri-cli --version "=2.0.0-beta.13" --locked

      - name: Tauri info
        run: cargo tauri info

      - uses: oNaiPs/secrets-to-env-action@v1
        with:
          secrets: ${{ toJSON(secrets) }}

      - name: Build Tauri App
        uses: tauri-apps/tauri-action@v0
        if: matrix.config.os != 'macos-latest' || matrix.config.arch != 'aarch64'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          releaseId: ${{ needs.create-release.outputs.release_id }}
          releaseBody: ${{ needs.create-release.outputs.release_body }}
          tauriScript: cargo tauri
          updaterJsonPreferNsis: true

      - name: Build Tauri App (macOS aarch64)
        uses: tauri-apps/tauri-action@v0
        if: matrix.config.os == 'macos-latest' && matrix.config.arch == 'aarch64'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          releaseId: ${{ needs.create-release.outputs.release_id }}
          releaseBody: ${{ needs.create-release.outputs.release_body }}
          args: --target aarch64-apple-darwin
          tauriScript: cargo tauri
          updaterJsonPreferNsis: true

  build-clip-extension:
    runs-on: ubuntu-22.04

    permissions:
      contents: write
      packages: write

    needs: create-release

    steps:
      - uses: actions/checkout@v4

      - name: Get version
        id: get_version
        uses: battila7/get-version-action@v2

      - name: Build PopClip extension
        run: make build-popclip-extension

      - name: Upload PopClip extension to release
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.release_upload_url }}
          asset_path: dist/openai-translator.popclipextz
          asset_name: openai-translator.popclipextz
          asset_content_type: application/zip

      - name: Build SnipDo extension
        run: make build-snipdo-extension

      - name: Upload SnipDo extension to release
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.release_upload_url }}
          asset_path: dist/openai-translator.pbar
          asset_name: openai-translator.pbar
          asset_content_type: application/zip

  build-browser-extension:
    runs-on: ubuntu-22.04

    permissions:
      contents: write
      packages: write

    needs: create-release

    steps:
      - uses: actions/checkout@v4

      - uses: pnpm/action-setup@v2
        with:
          version: 8.6.0

      - name: setup node
        uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'pnpm'

      - name: Get version
        id: get_version
        uses: battila7/get-version-action@v2

      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile

      - name: Build browser extension
        run: make build-browser-extension
        env:
          VERSION: "${{ steps.get_version.outputs.version-without-v }}"

      - name: Build userscript
        run: make build-userscript
        env:
          VERSION: "${{ steps.get_version.outputs.version-without-v }}"

      - name: Package plugin and create userscript
        run: |
          mkdir release
          mv dist/browser-extension/*.zip release/
          mv dist/openai-translator.user.js release/openai-translator-${{ steps.get_version.outputs.version-without-v }}.user.js

      - name: Upload Chromium extensions to release
        id: upload-chromium-release-asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.release_upload_url }}
          asset_path: release/chromium.zip
          asset_name: openai-translator-chromium-extension-${{ steps.get_version.outputs.version-without-v }}.zip
          asset_content_type: application/zip

      - name: Upload Firefox extensions to release
        id: upload-firefox-release-asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.release_upload_url }}
          asset_path: release/firefox.zip
          asset_name: openai-translator-firefox-extension-${{ steps.get_version.outputs.version-without-v }}.xpi
          asset_content_type: application/zip

      - name: Upload userscript to release
        id: upload-userscript-asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.release_upload_url }}
          asset_path: release/openai-translator-${{ steps.get_version.outputs.version-without-v }}.user.js
          asset_name: openai-translator-${{ steps.get_version.outputs.version-without-v }}.user.js
          asset_content_type: application/javascript

  publish-release:
    permissions:
      contents: write
    runs-on: ubuntu-20.04
    needs: [create-release, build-tauri, build-clip-extension, build-browser-extension]

    steps:
      - name: publish release
        id: publish-release
        uses: actions/github-script@v6
        env:
          release_id: ${{ needs.create-release.outputs.release_id }}
        with:
          script: |
            github.rest.repos.updateRelease({
              owner: context.repo.owner,
              repo: context.repo.repo,
              release_id: process.env.release_id,
              draft: false,
              prerelease: false
            })
