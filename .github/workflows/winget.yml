name: Publish to WinGet
on:
  release:
    types: [released]
jobs:
  publish:
    # Action can only be run on windows
    runs-on: windows-latest
    steps:
      - uses: vedantmgoyal2009/winget-releaser@v2
        with:
          identifier: yetone.OpenAITranslator
          max-versions-to-keep: 5 # keep only latest 5 versions
          installers-regex: '\.msi$' # Only .msi files
          token: ${{ secrets.WINGET_TOKEN }}
