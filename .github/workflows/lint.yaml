name: Lint

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  typos-check:
    name: Spell Check with Typos
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Actions Repository
        uses: actions/checkout@v4
      - name: Check spelling with custom config file
        uses: crate-ci/typos@v1.16.10
        with:
          config: ./typos.toml

  clippy:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@nightly
        with:
          components: clippy
      - run: mkdir -p ./dist/tauri
      - uses: giraffate/clippy-action@v1
        with:
          reporter: 'github-pr-review'
          github_token: ${{ secrets.GITHUB_TOKEN }}
          clippy_flags: -- -Dwarning
          workdir: ./src-tauri

  rustfmt:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
      checks: write
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@nightly
        with:
          components: rustfmt
      - uses: LoliGothick/rustfmt-check@master
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          flags: --all
          working-directory: ./src-tauri

  eslint:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - uses: pnpm/action-setup@v2
        with:
          version: 8.6.0

      - uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'pnpm'

      - name: Install packages
        run: pnpm install --no-frozen-lockfile

      - name: Run type check
        run: pnpm tsc

      - name: Run eslint
        run: pnpm lint
