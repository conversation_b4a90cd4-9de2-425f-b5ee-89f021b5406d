{"identifier": "migrated", "description": "permissions that were migrated from v1", "context": "local", "windows": ["translator", "settings", "action_manager", "updater", "thumb", "screenshot"], "permissions": [{"identifier": "http:default", "allow": [{"url": "https://*"}, {"url": "http://*"}]}, "path:default", "event:default", "webview:default", "webview:allow-create-webview", "webview:allow-create-webview-window", "webview:allow-internal-toggle-devtools", "webview:allow-print", "webview:allow-reparent", "webview:allow-set-webview-focus", "webview:allow-set-webview-position", "webview:allow-set-webview-size", "webview:allow-webview-close", "webview:allow-webview-position", "webview:allow-webview-size", "window:default", "window:allow-available-monitors", "window:allow-center", "window:allow-close", "window:allow-create", "window:allow-current-monitor", "window:allow-destroy", "window:allow-hide", "window:allow-inner-position", "window:allow-inner-size", "window:allow-internal-toggle-maximize", "window:allow-is-closable", "window:allow-is-decorated", "window:allow-is-focused", "window:allow-is-fullscreen", "window:allow-is-maximizable", "window:allow-is-maximized", "window:allow-is-minimizable", "window:allow-is-minimized", "window:allow-is-resizable", "window:allow-is-visible", "window:allow-maximize", "window:allow-minimize", "window:allow-outer-position", "window:allow-outer-size", "window:allow-primary-monitor", "window:allow-request-user-attention", "window:allow-scale-factor", "window:allow-set-always-on-bottom", "window:allow-set-always-on-top", "window:allow-set-closable", "window:allow-set-content-protected", "window:allow-set-cursor-grab", "window:allow-set-cursor-icon", "window:allow-set-cursor-position", "window:allow-set-cursor-visible", "window:allow-set-decorations", "window:allow-set-effects", "window:allow-set-focus", "window:allow-set-fullscreen", "window:allow-set-icon", "window:allow-set-ignore-cursor-events", "window:allow-set-max-size", "window:allow-set-maximizable", "window:allow-set-min-size", "window:allow-set-minimizable", "window:allow-set-position", "window:allow-set-progress-bar", "window:allow-set-resizable", "window:allow-set-shadow", "window:allow-set-size", "window:allow-set-skip-taskbar", "window:allow-set-title", "window:allow-set-visible-on-all-workspaces", "window:allow-show", "window:allow-start-dragging", "window:allow-theme", "window:allow-title", "window:allow-toggle-maximize", "window:allow-unmaximize", "window:allow-unminimize", "app:default", "app:allow-app-show", "app:allow-app-hide", "resources:default", "menu:default", "tray:default", "updater:default", "aptabase:allow-track-event", "shell:allow-execute", "shell:allow-kill", "shell:allow-open", "shell:allow-stdin-write", "autostart:allow-is-enabled", "autostart:allow-enable", "autostart:allow-disable", "fs:allow-app-write", "fs:scope-app", "fs:scope-appcache", "fs:scope-appconfig", "fs:scope-appdata", "fs:scope-applocaldata", "fs:scope-applog", "fs:scope-desktop", "fs:allow-read-file", "fs:allow-write-file", "fs:allow-read-dir", "fs:allow-copy-file", "fs:allow-mkdir", "fs:allow-remove", "fs:allow-remove", "fs:allow-rename", "fs:allow-exists", "global-shortcut:allow-is-registered", "global-shortcut:allow-register", "global-shortcut:allow-register-all", "global-shortcut:allow-unregister", "global-shortcut:allow-unregister-all", "os:allow-platform", "os:allow-version", "os:allow-os-type", "os:allow-family", "os:allow-arch", "os:allow-exe-extension", "os:allow-locale", "os:allow-hostname", "process:allow-restart", "process:allow-exit", "notification:default", "notification:allow-is-permission-granted"], "platforms": ["linux", "macOS", "windows", "android", "iOS"]}