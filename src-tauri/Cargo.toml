[package]
name = "app"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
license = ""
repository = ""
default-run = "app"
edition = "2021"
rust-version = "1.59"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "=2.0.0-beta.15", features = [] }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = [ "derive" ] }
tauri = { version = "=2.0.0-beta.19", features = [ "macos-private-api", "protocol-asset", "tray-icon" ] }
once_cell = "1.17.1"
clipboard = "0.5.0"
enigo = { version = "0.2.0", features = [ "xdo" ] }
mouse_position = "0.1.3"
rdev = "0.5.2"
tauri-utils = { version = "=2.0.0-beta.15", features = [] }
tauri-plugin-single-instance = { git = "https://github.com/tauri-apps/plugins-workspace", rev = "723b9f7fa85116efdc63b04b444a60e76a950ab7" }
tauri-plugin-autostart = { git = "https://github.com/tauri-apps/plugins-workspace", rev = "723b9f7fa85116efdc63b04b444a60e76a950ab7" }
cpuid = "0.1.1"
sysinfo = "0.28.3"
parking_lot = "0.12.1"
mouce = "0.2.41"
tauri-plugin-notification = { git = "https://github.com/tauri-apps/plugins-workspace", rev = "723b9f7fa85116efdc63b04b444a60e76a950ab7" }
tauri-plugin-http = { git = "https://github.com/tauri-apps/plugins-workspace", rev = "723b9f7fa85116efdc63b04b444a60e76a950ab7" }
tauri-plugin-global-shortcut = { git = "https://github.com/tauri-apps/plugins-workspace", rev = "7fc29c326bc463d59c7e2ee04052b5d81796e407" }
tauri-plugin-updater = { git = "https://github.com/tauri-apps/plugins-workspace", rev = "723b9f7fa85116efdc63b04b444a60e76a950ab7" }
tauri-plugin-process = { git = "https://github.com/tauri-apps/plugins-workspace", rev = "723b9f7fa85116efdc63b04b444a60e76a950ab7" }
tauri-plugin-fs = { git = "https://github.com/tauri-apps/plugins-workspace", rev = "723b9f7fa85116efdc63b04b444a60e76a950ab7" }
tauri-plugin-shell = { git = "https://github.com/tauri-apps/plugins-workspace", rev = "723b9f7fa85116efdc63b04b444a60e76a950ab7" }
tauri-plugin-os = { git = "https://github.com/tauri-apps/plugins-workspace", rev = "723b9f7fa85116efdc63b04b444a60e76a950ab7" }
whatlang = "0.16.2"
arboard = "3.2.0"
tiny_http = "0.12.0"
text-diff = "0.4.0"
similar = "2.2.1"
debug_print = "1.0.0"
active-win-pos-rs = "0.8"
reqwest = { version = "0.11.24", features = [ "json", "stream" ] }
tokio = { version = "1", features = [ "full" ] }
futures-util = "0.3.29"
tauri-plugin-aptabase = { git = "https://github.com/aptabase/tauri-plugin-aptabase", rev = "4e2422a6ad3f73d0ba7b9696b9f3f64beb13295b" }
screenshots = "0.7.2"
image = "0.24.7"
get-selected-text = "0.1.6"
specta = "=2.0.0-rc.12"
tauri-specta = { version = "2.0.0-rc.10", features = ["javascript", "typescript"] }

[target.'cfg(target_os = "macos")'.dependencies]
cocoa = "0.24"
objc = "0.2.7"
macos-accessibility-client = "0.0.1"
core-foundation = "0.9.3"
core-graphics = "0.22.3"
accessibility-ng = "0.1.6"
accessibility-sys-ng = "0.1.3"

[target.'cfg(windows)'.dependencies]
windows = { version = "0.56.0", features = [ "Win32_UI_WindowsAndMessaging", "Win32_Foundation", "Graphics_Imaging", "Media_Ocr", "Foundation", "Foundation_Collections", "Globalization", "Storage", "Storage_Streams" ] }

[features]
# by default Tauri runs in production mode
# when `tauri dev` runs it is executed with `cargo run --no-default-features` if `devPath` is an URL
default = ["custom-protocol"]
# this feature is used for production builds where `devPath` points to the filesystem
# DO NOT remove this
custom-protocol = ["tauri/custom-protocol"]
