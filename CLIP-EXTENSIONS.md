Desktop Application Global Clip Extensions
------------------------------------------

<p align="center">
    <br> English | <a href="CLIP-EXTENSIONS-CN.md">中文</a>
</p>

Clip translation is the killer feature of this software. For browser plugins, the browser provides a simple API to get the selected text, but for desktop applications, there is no unified API for each operating system to get the selected text.

Usually the clipboard is used to get the selected text, but this can cause bugs such as clipboard clutter in some applications and a warning sound in macOS because the cmd+c shortcut is pressed without the text selected. Fortunately, there are many mature clip software for various operating systems and they have a good plug-in mechanism, so OpenAI Translator has developed plug-ins for these clip software to allow users to use paddleboarding translation painlessly.

# macOS

## PopClip

[PopClip](https://pilotmoon.com/popclip/) is a well-established clip word software on macOS, it provides a perfect plug-in mechanism, we provide its plug-in, the installation steps are as follows:

* 1. Download and install [PopClip](https://pilotmoon.com/popclip/)
* 2. Download [openai-translator.popclipextz](https://github.com/openai-translator/openai-translator/releases/latest/download/openai-translator.popclipextz)
* 3. Double-click the downloaded openai-translator.popclipextz and click the Install "OpenAI Translator" button in the popup window to finish the installation
    
    <p align="center">
        <img width="400" src="https://user-images.githubusercontent.com/1206493/240260692-8af6141a-3dba-4775-921d-505223addf9e.png" />
    </p>

* 4. Open OpenAI Translator in PopClip
    
    <p align="center">
        <img width="400" src="https://user-images.githubusercontent.com/1206493/240258859-c4f2ec91-255f-414c-a4a4-aca25fceb0b5.png" />
    </p>

* 5. The effect is as follows

    <p align="center">
        <img width="600" src="https://user-images.githubusercontent.com/1206493/240355949-8f41d98d-f097-4ce4-a533-af60e1757ca1.gif" />
    </p>

## Windows

## SnipDo

* 1. Download and install [SnipDo](https://apps.microsoft.com/store/detail/snipdo/9NPZ2TVKJVT7)
* 2. Download [openai-translator.pbar](https://github.com/openai-translator/openai-translator/releases/latest/download/openai-translator.pbar)
* 3. Double-click the downloaded openai-translator.pbar to install it
* 4. Enable OpenAI Translator in SnipDo's settings page
    <p align="center">
        <img width="200" src="https://github.com/openai-translator/openai-translator/assets/1206493/09d66943-06db-4ba7-b217-a434c33cc8aa" />
    </p>

    Suggest to only keep OpenAI Translator:
  
    <p align="center">    
        <img width="600" src="https://github.com/openai-translator/openai-translator/assets/1206493/76b619d9-e63d-4d67-a32c-a0d2d6923558" />
    </p>
    
* 5. The effect is as follows

    <p align="center">
        <img width="600" src="https://user-images.githubusercontent.com/1206493/240358161-2788eb97-d00b-4808-aa86-a7fcfe3f71dd.gif" />
    </p>

