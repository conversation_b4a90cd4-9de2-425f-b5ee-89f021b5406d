<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta
            name="viewport"
            content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
        />
        <meta http-equiv="X-UA-Compatible" content="ie=edge" />
        <script type="module" src="/src/tauri/index.tsx"></script>
        <title>OpenAI Translator</title>
        <style>
            html, body {
                margin: 0px;
                padding: 0px;
                overscroll-behavior: none;
                scrollbar-width: none;
                -ms-overflow-style: none;
                -ms-user-select: none;
                -webkit-user-select: none;
                user-select: none;
                background: transparent;
            }

            /* custom scrollbar */
            *::-webkit-scrollbar {
                display: none;
            }

            textarea::-webkit-resizer {
                background: url(../common/assets/images/resizer.svg) 50% 50% no-repeat;
            }

            textarea::-webkit-scrollbar {
                display: block;
                width: 20px;
            }

            textarea::-webkit-scrollbar-corner {
                display: none;
            }

            textarea::-webkit-scrollbar-button {
                display: none;
            }

            textarea::-webkit-scrollbar-track {
                background-color: transparent;
            }

            textarea::-webkit-scrollbar-thumb {
                background-color: #d6dee1;
                border-radius: 20px;
                border: 6px solid transparent;
                background-clip: content-box;
            }

            textarea::-webkit-scrollbar-thumb:hover {
                background-color: #a8bbbf;
            }

            *, a, button {
                -ms-user-select: none;
                -webkit-user-select: none;
                user-select: none;
            }

            input, textarea {
                -webkit-user-select: text;
            }

            [data-baseweb='tooltip'],
            [data-baseweb='toaster'],
            [data-baseweb='modal'],
            [data-baseweb='popover'] {
                z-index: 1000;
            }
        </style>
    </head>
    <body style="position: relative; min-height: 100vh">
        <div id="root"></div>
    </body>
</html>
