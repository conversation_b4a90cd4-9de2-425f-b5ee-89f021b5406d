// This file was generated by [tauri-specta](https://github.com/oscartbeaumont/tauri-specta). Do not edit this file manually.

export const commands = {
    async getConfigContent(): Promise<string> {
        return await TAURI_INVOKE('get_config_content')
    },
    async getUpdateResult(): Promise<[boolean, UpdateResult | null]> {
        return await TAURI_INVOKE('get_update_result')
    },
    async clearConfigCache(): Promise<void> {
        await TAURI_INVOKE('clear_config_cache')
    },
    async showTranslatorWindowCommand(): Promise<void> {
        await TAURI_INVOKE('show_translator_window_command')
    },
    async showTranslatorWindowWithSelectedTextCommand(): Promise<void> {
        await TAURI_INVOKE('show_translator_window_with_selected_text_command')
    },
    async showActionManagerWindow(): Promise<void> {
        await TAURI_INVOKE('show_action_manager_window')
    },
    async getTranslatorWindowAlwaysOnTop(): Promise<boolean> {
        return await TAURI_INVOKE('get_translator_window_always_on_top')
    },
    async fetchStream(id: string, url: string, optionsStr: string): Promise<Result<string, string>> {
        try {
            return { status: 'ok', data: await TAURI_INVOKE('fetch_stream', { id, url, optionsStr }) }
        } catch (e) {
            if (e instanceof Error) throw e
            else return { status: 'error', error: e as any }
        }
    },
    async writingCommand(): Promise<void> {
        await TAURI_INVOKE('writing_command')
    },
    async writeToInput(text: string): Promise<void> {
        await TAURI_INVOKE('write_to_input', { text })
    },
    async finishWriting(): Promise<void> {
        await TAURI_INVOKE('finish_writing')
    },
    async detectLang(text: string): Promise<string> {
        return await TAURI_INVOKE('detect_lang', { text })
    },
    async screenshot(x: number, y: number): Promise<void> {
        await TAURI_INVOKE('screenshot', { x, y })
    },
    async hideTranslatorWindow(): Promise<void> {
        await TAURI_INVOKE('hide_translator_window')
    },
    async startOcr(): Promise<void> {
        await TAURI_INVOKE('start_ocr')
    },
    async finishOcr(): Promise<void> {
        await TAURI_INVOKE('finish_ocr')
    },
    async cutImage(left: number, top: number, width: number, height: number): Promise<void> {
        await TAURI_INVOKE('cut_image', { left, top, width, height })
    },
}

export const events = __makeEvents__<{
    checkUpdateEvent: CheckUpdateEvent
    checkUpdateResultEvent: CheckUpdateResultEvent
    pinnedFromWindowEvent: PinnedFromWindowEvent
    pinnedFromTrayEvent: PinnedFromTrayEvent
    configUpdatedEvent: ConfigUpdatedEvent
}>({
    checkUpdateEvent: 'check-update-event',
    checkUpdateResultEvent: 'check-update-result-event',
    pinnedFromWindowEvent: 'pinned-from-window-event',
    pinnedFromTrayEvent: 'pinned-from-tray-event',
    configUpdatedEvent: 'config-updated-event',
})

/** user-defined types **/

export type CheckUpdateEvent = null
export type CheckUpdateResultEvent = UpdateResult
export type ConfigUpdatedEvent = null
export type PinnedFromTrayEvent = { pinned: boolean }
export type PinnedFromWindowEvent = { pinned: boolean }
export type UpdateResult = { version: string; currentVersion: string; body: string | null }

/** tauri-specta globals **/

import { invoke as TAURI_INVOKE } from '@tauri-apps/api/core'
import * as TAURI_API_EVENT from '@tauri-apps/api/event'
import { type WebviewWindow as __WebviewWindow__ } from '@tauri-apps/api/webviewWindow'

type __EventObj__<T> = {
    listen: (cb: TAURI_API_EVENT.EventCallback<T>) => ReturnType<typeof TAURI_API_EVENT.listen<T>>
    once: (cb: TAURI_API_EVENT.EventCallback<T>) => ReturnType<typeof TAURI_API_EVENT.once<T>>
    emit: T extends null
        ? (payload?: T) => ReturnType<typeof TAURI_API_EVENT.emit>
        : (payload: T) => ReturnType<typeof TAURI_API_EVENT.emit>
}

export type Result<T, E> = { status: 'ok'; data: T } | { status: 'error'; error: E }

function __makeEvents__<T extends Record<string, any>>(mappings: Record<keyof T, string>) {
    return new Proxy(
        {} as unknown as {
            [K in keyof T]: __EventObj__<T[K]> & {
                (handle: __WebviewWindow__): __EventObj__<T[K]>
            }
        },
        {
            get: (_, event) => {
                const name = mappings[event as keyof T]

                return new Proxy((() => {}) as any, {
                    apply: (_, __, [window]: [__WebviewWindow__]) => ({
                        listen: (arg: any) => window.listen(name, arg),
                        once: (arg: any) => window.once(name, arg),
                        emit: (arg: any) => window.emit(name, arg),
                    }),
                    get: (_, command: keyof __EventObj__<any>) => {
                        switch (command) {
                            case 'listen':
                                return (arg: any) => TAURI_API_EVENT.listen(name, arg)
                            case 'once':
                                return (arg: any) => TAURI_API_EVENT.once(name, arg)
                            case 'emit':
                                return (arg: any) => TAURI_API_EVENT.emit(name, arg)
                        }
                    },
                })
            },
        }
    )
}
