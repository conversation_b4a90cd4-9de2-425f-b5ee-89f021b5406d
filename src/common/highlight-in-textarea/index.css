.yetone-hit-container {
    display: inline-block;
    position: relative;
    overflow: hidden !important;
    -webkit-text-size-adjust: none !important;
    width: 100% !important;
}

.yetone-hit-container textarea {
    display: block;
}

.yetone-hit-backdrop {
    position: absolute !important;
    top: 0 !important;
    right: -99px !important;
    bottom: 0 !important;
    left: 0 !important;
    padding-right: 99px !important;
    overflow-x: hidden !important;
    overflow-y: auto !important;
}

.yetone-hit-highlights {
    width: auto !important;
    height: auto !important;
    border-color: transparent !important;
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
    color: transparent !important;
    overflow: hidden !important;
}

.yetone-hit-input {
    display: block !important;
    position: relative !important;
    margin: 0;
    padding: 0;
    border-radius: 0;
    font: inherit;
    overflow-x: hidden !important;
    overflow-y: auto !important;
}

.yetone-hit-content {
    padding: 4px 8px;
    background: none transparent !important;
}

.yetone-hit-content mark {
    padding: 1px 2px !important;
    margin-left: -2px !important;
    margin-top: 1px !important;
    color: transparent !important;
    border-radius: 0.2rem;
    background-color: #f8c3cd !important;
}

.yetone-dark .yetone-hit-content mark {
    color: rgb(203, 203, 203);
    background-color: #b5495b !important;
}
