{"Translate": "翻译", "Summarize": "总结", "Polishing": "润色", "Analyze": "分析", "Explain Code": "解释代码", "Nop": "Nop", "Follow the System": "跟随系统", "Dark": "暗色", "Light": "亮色", "Clear input": "清空输入", "Copy to clipboard": "复制到剪贴板", "Speak": "朗读", "Upload an image for OCR translation": "上传图像进行OCR翻译", "Default service provider": "默认服务提供商", "Default translate mode": "默认翻译模式", "Auto Translate": "自动翻译", "Word selection in input": "输入框划词", "Read the selected words in input": "朗读输入框中选中的单词", "Always show icons": "始终显示图标", "Default target language": "默认目标语言", "Theme": "主题", "i18n": "语言", "Hotkey": "快捷键", "OCR Hotkey": "OCR 快捷键", "Writing Hotkey": "写作快捷键", "Please press the hotkey you want to set.": "请按您要设置的快捷键。", "Click above to set hotkeys.": "点击上面设置快捷键。", "Save": "保存", "Saved": "保存成功", "Go to Translator": "前往翻译器", "Go to Settings": "前往设置", "API Key": "API 密钥", "API URL": "API URL", "API URL Path": "API URL 路径", "is required": "是必填项", "Go to the": "前往", "OpenAI page": "OpenAI 页面", "MiniMax page": "MiniMax 页面", "to get your API Key. You can separate multiple API Keys with English commas to achieve quota doubling and load balancing.": "获取您的 API 密钥。您可以使用英文逗号分隔多个 API 密钥，以实现配额加倍和负载均衡。", "to get your API Key.": "获取您的 API 密钥。", "to get your Group ID.": "获取您的 Group ID。", "Restore Previous Position": "恢复上次位置", "Fixed Position": "固定位置", "Run at startup": "开机启动", "API Model": "API 模型", "Chars Limited": "字数超出限制", "Country Not Supported": "您正在直接连接至 OpenAI，而您的 IP 属地为 {{name}}，不在 OpenAI <0>支持的地区</0> 范围内。如果不进行额外的网络配置继续使用，即使订阅了 ChatGPT Plus 或账号内仍有余额，您的账户也会被 OpenAI 封禁。", "Country Not Detected": "我们无法检查您的 IP 地址是否位于 OpenAI 的<0>支持的地区</0>，可能是由于您的网络环境无法访问 OpenAI。请检查您的网络连接，并确保您在支持的位置访问 API，否则即使订阅了 ChatGPT Plus 或账号内仍有余额，您的账户也会被 OpenAI 封禁。", "words are collected": "已收藏 {{collectTotal}} 个单词", "Collection Review": "单词复习", "Export your collection as a csv file": "导出收藏为 csv 文件", "CSV file saved on Desktop": "csv 文件已保存在桌面", "Remove from collection": "移出收藏", "Add to collection": "加入收藏", "Auto collect": "自动加入收藏", "Random Change": "下一批", "Please select an essay type": "请选择文章类型", "is writing": "正在创作中", "An insteresting story": "一个有趣的故事", "A political newsletter": "一篇时政新闻", "A sports bulletin": "一则体育快讯", "A catchy lyric": "一篇简易的歌词", "A smooth poem": "一首流畅的诗", "No article type selected": "未选中文章类型", "review count": "查询次数", "last review": "最近查询时间", "vocabulary": "词汇表", "generate article": "生成文章", "insert to editor": "插入编辑器", "Show button when text is selected": "选中文字时显示按钮", "Show icon when text is selected": "选中文字时显示图标", "Using clipboard": "使用剪贴板", "Allow using the clipboard to get the selected text when the selected text is not available": "当无法获得选中文本时允许使用剪贴板来获取选中文本", "Please login to ChatGPT Web": "请登录 ChatGPT Web", "It is highly recommended to disable this feature and use the Clip Extension": "强烈建议您关闭此功能并使用 Clip 扩展", "Clip Extension": "Clip 扩展", "built-in": "内置", "Default Action": "默认动作", "Created at": "创建时间", "Updated at": "更新时间", "Name": "名称", "Icon": "图标", "Description": "描述", "Placeholders": "占位符", "Role Prompt": "角色提示词", "Role prompt indicates what role the action represents.": "角色提示词指示了该动作代表的角色。", "Role prompt example: You are a translator.": "角色提示词示例：你是一位翻译专家。", "Command Prompt": "命令提示词", "Command prompt indicates what command should be issued to the role represented by the action when the action is executed.": "命令提示词指示了当执行该动作时，应向该动作所代表的角色发出什么命令。", "Command prompt example: Please translate the following text from ${sourceLang} to ${targetLang}.": "命令提示词示例：请将以下文本从 ${sourceLang} 翻译成 ${targetLang}。", "Create": "创建", "Delete": "删除", "Update": "更新", "Action": "动作", "Action Manager": "动作管理器", "Update sth": "更新 {{0}}", "Create sth": "创建 {{0}}", "Delete sth": "删除 {{0}}", "Are you sure to delete sth?": "确定要删除 {{0}} 吗？", "Ok": "确定", "Cancel": "取消", "Submit": "提交", "represents the source language": "代表源语言", "represents the target language": "代表目标语言", "represents the original text, which is usually not needed inside the prompt because it is automatically injected": "代表原始文本，通常不需要在提示词中使用，因为它会自动注入", "Output rendering format": "输出渲染格式", "disable collecting statistics": "禁用统计", "Buy me a coffee": "请我喝杯咖啡", "Writing target language": "写作目标语言", "Press this shortcut key in the input box of any application, and the text already entered in the input box will be automatically translated into the writing target language.": "在任何应用程序的输入框中按下此快捷键，输入框中已输入的文本将自动翻译为写作目标语言。", "Writing line break shortcut": "写作换行快捷键", "When writing, which key should be pressed when encountering a line break?": "写作时，遇到换行时应按哪个键？", "It is recommended to download the desktop application of OpenAI Translator to enjoy the wonderful experience of word translation in all software!": "推荐下载 OpenAI Translator 的桌面应用程序，以享受在所有软件中进行单词翻译的精彩体验！", "Download Link": "下载链接", "General": "通用", "TTS": "TTS", "Writing": "写作", "Shortcuts": "快捷键", "Advanced": "高级", "About": "关于", "Provider": "提供者", "Rate": "速率", "Volume": "音量", "Voice": "声音", "Slow": "慢", "Fast": "快", "Quiet": "安静", "Loud": "响亮", "Add": "添加", "Go back": "返回", "Updater": "更新器", "Checking for the latest version ...": "正在检查最新版本...", "Congratulations! You are now using the latest version!": "恭喜！您现在正在使用最新版本！", "A new version is available!": "有新版本可用！", "The current version is {{0}}, and the latest version is": "当前版本为 {{0}}，最新版本为", "The release content:": "发布内容：", "The update content:": "更新内容：", "Close": "关闭", "Automatic check for updates": "自动检查更新", "Enable mica (Experimental)": "启用云母材质（实验性）", "**Win11 only. If the mica effect is enabled, it is essential to set the `Theme` to `Follow the system`, as it is currently not possible to manually switch between the light and dark themes of mica.": "**仅限Win11。如果启用了 mica 效果，务必将`主题`设置为`跟随系统`，因为目前无法手动在 mica 的浅色和暗色主题之间切换。", "Display window Hotkey": "显示窗口快捷键", "Hide the icon in the Dock bar": "隐藏 Dock 栏中的图标", "Disagree": "不同意", "Agree and continue": "同意并继续", "Disclaimer": "免责声明", "How to Use": "使用方法", "Please select a language": "请选择一种语言", "Please select a voice": "请选择一种声音", "Language detection engine": "语种检测引擎", "Google": "谷歌", "Baidu": "百度", "Bing": "必应", "Auto hide window when out of focus": "失去焦点时自动隐藏窗口", "Proxy": "代理", "Enabled": "启用", "Protocol": "协议", "Server": "服务器", "Port": "端口", "Username": "用户名", "Password": "密码", "No proxy": "无代理", "Test proxy": "测试代理", "Test": "测试", "Result": "结果", "Your proxy is working fine": "您的代理工作正常", "Your proxy is not working": "您的代理不工作", "Location": "位置", "Time consumed": "耗时", "Custom": "自定义", "Custom Model Name": "自定义模型名称", "Local": "本地", "Local Model": "本地模型", "to learn how to install and setup.": "学习如何安装和设置。", "Model needs to first use the `ollama pull` command to download locally, please view all models from this page:": "模型需要首先使用 `ollama pull` 命令下载到本地，请从此页面查看所有模型：", "Generally, there is no need to modify this item.": "一般情况下，无需修改此项。", "Hide the icon in the taskbar": "隐藏任务栏中的图标", "Please login to Kimi Web": "请登录 Kimi Web", "Please login to ChatGLM Web": "请登录智谱清言 Web", "Free": "免费", "to get your refresh_token.": "获取您的 refresh_token。", "to get your access_token.": "获取您的 access_token。", "to get your token.": "获取您的 token。", "to get the solutions.": "获取解决方案。", "ChatGLM": "智谱清言", "Font size": "字体大小", "Font family": "字体", "UI font size": "UI 字体大小", "UI font family": "UI 字体", "Icon size": "图标大小", "Window background blur": "窗口背景模糊", "For Windows 11 only. If the window background blur effect is enabled, please ensure to set the 'Theme' to 'Follow the System', as it is currently not possible to manually switch between light and dark themes when the window background blur is active.": "仅限 Windows 11。如果启用了窗口背景模糊效果，请确保将“主题”设置为“跟随系统”，因为目前无法在窗口背景模糊激活时手动切换浅色和暗色主题。", "If the window background blur effect is enabled, please ensure to set the 'Theme' to 'Follow the System', as it is currently not possible to manually switch between light and dark themes when the window background blur is active.": "如果启用了窗口背景模糊效果，请确保将“主题”设置为“跟随系统”，因为目前无法在窗口背景模糊激活时手动切换浅色和暗色主题。", "Seconds": "秒", "Minutes": "分钟", "Hours": "小时", "Days": "天", "Forever": "永久", "The survival time of the Ollama model in memory": "Ollama 模型在内存中的存活时间", "No models API support": "无模型 API 支持", "Some providers claiming to be compatible with OpenAI's API do not actually support OpenAI's standard model API. Therefore, we have no choice but to offer this option. If you choose this option (and then need to click the save button), we will not attempt to dynamically fetch the latest model list from the model API, but will only use a fixed model list and custom models.": "一些声称兼容 OpenAI API 的提供商竟然不支持 OpenAI 的标准的模型 API。因此，我们别无选择，只能提供此选项。如果您选择此选项（然后需要单击保存按钮），我们将不会尝试从模型 API 动态获取最新模型列表，而只会使用固定的模型列表和自定义模型。", "If you find this tool helpful, you can buy me a cup of coffee.": "如果您觉得这个工具对您有所帮助，您可以请我喝杯咖啡。"}