{"Translate": "翻訳", "Summarize": "要約", "Polishing": "推敲", "Analyze": "分析", "Explain Code": "コードを説明する", "Nop": "Nop", "Follow the System": "システムに従う", "Dark": "ダーク", "Light": "ライト", "Clear input": "クリア入力", "Copy to clipboard": "クリップボードにコピー", "Speak": "音声を聞く", "Upload an image for OCR translation": "OCR翻訳のための画像をアップロード", "Default service provider": "デフォルトのサービスプロバイダ", "Default translate mode": "デフォルトの翻訳モード", "Auto Translate": "自動翻訳", "Word selection in input": "テキスト入力エレメント内で選択可能", "Read the selected words in input": "入力ボックスで選択された単語を読み上げる", "Always show icons": "常にアイコンを表示", "Default target language": "デフォルトのターゲット言語", "Theme": "テーマ", "i18n": "言語", "Hotkey": "ホットキー", "OCR Hotkey": "OCRホットキー", "Writing Hotkey": "書き込みホットキー", "Please press the hotkey you want to set.": "設定したいホットキーを押してください。", "Click above to set hotkeys.": "ホットキーを設定するには上をクリックしてください。", "Save": "保存", "Saved": "正常に保存されました", "Go to Translator": "翻訳に移動", "Go to Settings": "設定に移動", "API Key": "API キー", "API URL": "API URL", "API URL Path": "API URL パス", "is required.": "は必須です。", "Go to the": "", "OpenAI page": "OpenAIのページ", "MiniMax page": "MiniMaxのページ", "to get your API Key. You can separate multiple API Keys with English commas to achieve quota doubling and load balancing.": "にアクセスして、APIキーを取得してください。複数のAPIキーをカンマで区切ることで、利用制限を緩和し、システムへの負荷を軽減することができます。", "to get your API Key.": "にアクセスして、APIキーを取得してください。", "to get your Group ID.": "にアクセスして、グループIDを取得してください。", "Restore Previous Position": "以前の位置を復元", "Fixed Position": "固定位置", "Run at startup": "起動時に実行", "API Model": "API モデル", "Chars Limited": "文字数が制限を超えています", "Country Not Supported": "IPアドレスは{{name}}からであり、それはOpenAIの <0>対応していない地域</0> です。追加のネットワーク設定なしで使用を続けると、ChatGPT Plusのサブスクリプション状況やアカウント残高に関わらず、OpenAIによってアカウントがブロックされる可能性があります。", "Country Not Detected": "OpenAIのサービスが <0>対応している地域</0> からアクセスされているかを確認できませんでした。これは、OpenAIへのアクセスに影響するネットワーク接続の問題が原因かもしれません。ネットワーク接続を確認して、サポートされている地域からAPIにアクセスしていることを確認してください。サポートされている地域からアクセスしない場合、ChatGPT Plusに加入している場合や口座にまだ残高がある場合でも、OpenAIによってアクセスがブロックされる可能性があります。", "words are collected": "{{collectTotal}} つの単語を収集しました", "Collection review": "単語を復習", "Export your collection as a csv file": "CSVファイルとしてコレクションをエクスポート", "CSV file saved on Desktop": "デスクトップに保存されたCSVファイル", "Remove from collection": "コレクションから削除", "Add to collection": "コレクションに追加", "Auto collect": "自動的にコレクションに追加", "Random Change": "ランダム変化", "Please select an essay type": "記事の種類を選択してください", "is writing": "作成中", "An insteresting story": "魅力的な物語", "A political newsletter": "政治時事通信", "A sports bulletin": "スポーツ公告", "A catchy lyric": "朗々とした歌詞", "A smooth poem": "流暢な詩", "No article type selected": "記事タイプが選択されていません", "review count": "クエリ回数", "last review": "最近のクエリ時間", "vocabulary": "語彙", "generate article": "記事を生成", "insert to editor": "エディタに挿入", "Show button when text is selected": "テキストが選択されているときにボタンを表示", "Show icon when text is selected": "テキストが選択されているときにアイコンを表示", "Using clipboard": "クリップボードを使用", "Allow using the clipboard to get the selected text when the selected text is not available": "選択されたテキストが利用できない場合、クリップボードを使用して選択されたテキストを取得することを許可する", "Please login to ChatGPT Web": "ChatGPT Webにログインしてください", "It is highly recommended to disable this feature and use the Clip Extension": "この機能を無効にしてClip拡張機能を使用することを強くお勧めします", "Clip Extension": "Clip拡張機能", "built-in": "組み込み", "Default Action": "デフォルトアクション", "Created at": "作成日", "Updated at": "更新日", "Name": "名前", "Icon": "アイコン", "Description": "説明", "Placeholders": "プレースホルダー", "Role Prompt": "ロールプロンプト", "Role prompt indicates what role the action represents.": "ロールプロンプトは、アクションがどのような役割を表しているかを示します。", "Role prompt example: You are a translator.": "ロールプロンプトの例：あなたは翻訳者です。", "Command Prompt": "コマンドプロンプト", "Command prompt indicates what command should be issued to the role represented by the action when the action is executed.": "コマンドプロンプトは、アクションが実行されると、アクションが表す役割に対して発行するコマンドを示します。", "Command prompt example: Please translate the following text from ${sourceLang} to ${targetLang}.": "コマンドプロンプトの例：以下のテキストを${sourceLang}から${targetLang}に翻訳してください。", "Create": "作成", "Delete": "削除", "Update": "更新", "Action": "アクション", "Action Manager": "アクションマネージャー", "Update sth": "更新{{0}}", "Create sth": "作成{{0}}", "Delete sth": "削除{{0}}", "Are you sure to delete sth?": "本当に{{0}}を削除してもよろしいですか？", "Ok": "はい", "Cancel": "キャンセル", "Submit": "提出", "represents the source language": "はソース言語を表します", "represents the target language": "はターゲット言語を表します", "represents the original text, which is usually not needed inside the prompt because it is automatically injected": "は元のテキストを表します。通常、プロンプト内では必要ありません。自動的に挿入されるためです", "Output rendering format": "出力レンダリング形式", "disable collecting statistics": "統計情報の収集を無効にする", "Buy me a coffee": "コーヒーを買ってください", "Writing target language": "ターゲット言語を書く", "Press this shortcut key in the input box of any application, and the text already entered in the input box will be automatically translated into the writing target language.": "このショートカットキーを任意のアプリケーションの入力ボックスで押すと、入力ボックスにすでに入力されているテキストが自動的にターゲット言語に翻訳されます。", "Writing line break shortcut": "改行ショートカットを書く", "When writing, which key should be pressed when encountering a line break?": "書くとき、改行に遭遇したときにどのキーを押す必要がありますか？", "It is recommended to download the desktop application of OpenAI Translator to enjoy the wonderful experience of word translation in all software!": "すべてのソフトウェアで単語翻訳の素晴らしい体験をお楽しみいただくために、OpenAI Translatorのデスクトップアプリケーションをダウンロードすることをお勧めします！", "Download Link": "ダウンロードリンク", "General": "一般", "TTS": "TTS", "Writing": "書く", "Shortcuts": "ショートカット", "Advanced": "高度な", "About": "約", "Provider": "プロバイダー", "Rate": "レート", "Volume": "ボリューム", "Voice": "音声", "Slow": "遅い", "Fast": "速い", "Quiet": "静か", "Loud": "大声", "Add": "追加", "Go back": "戻る", "Updater": "更新プログラム", "Checking for the latest version ...": "最新バージョンを確認中...", "Congratulations! You are now using the latest version!": "おめでとうございます！あなたは今最新バージョンを使用しています！", "A new version is available!": "新しいバージョンが利用可能です！", "The current version is {{0}}, and the latest version is": "現在のバージョンは{{0}}で、最新バージョンは", "The release content:": "リリース内容：", "The update content:": "更新内容：", "Close": "閉じる", "Automatic check for updates": "自動更新チェック", "Enable mica (Experimental)": "実験的なマカ（実験的）", "**Win11 only. If the mica effect is enabled, it is essential to set the `Theme` to `Follow the system`, as it is currently not possible to manually switch between the light and dark themes of mica.": "Win11専用です。ミカ効果が有効になっている場合は、手動でミカのライトテーマとダークテーマを切り替えることができないため、`テーマ`を`システムに従う`に設定することが不可欠です。", "Display window Hotkey": "ウィンドウホットキーを表示", "Hide the icon in the Dock bar": "Dockバーのアイコンを非表示にする", "Disagree": "同意しない", "Agree and continue": "同意して続行", "Disclaimer": "免責事項", "How to Use": "使い方", "Please select a language": "言語を選択してください", "Please select a voice": "声を選択してください", "Language detection engine": "言語検出エンジン", "Google": "Google", "Baidu": "Baidu", "Bing": "<PERSON>", "Auto hide window when out of focus": "フォーカスが外れたときにウィンドウを自動的に非表示にする", "Proxy": "プロキシ", "Enabled": "有効", "Protocol": "プロトコル", "Server": "サーバー", "Port": "ポート", "Username": "ユーザー名", "Password": "パスワード", "No proxy": "プロキシなし", "Test proxy": "プロキシをテストする", "Test": "テスト", "Result": "結果", "Your proxy is working fine": "あなたのプロキシは正常に動作しています", "Your proxy is not working": "あなたのプロキシは動作していません", "Location": "ロケーション", "Time consumed": "消費された時間", "Custom": "カスタム", "Custom Model Name": "カスタムモデル名", "Local": "Local", "Local Model": "ローカルモデル", "to learn how to install and setup.": "インストールとセットアップ方法を学ぶ。", "Model needs to first use the `ollama pull` command to download locally, please view all models from this page:": "モデルは最初に`ollama pull`コマンドを使用してローカルにダウンロードする必要があります。このページからすべてのモデルを表示してください：", "Generally, there is no need to modify this item.": "一般的に、この項目を変更する必要はありません。", "Hide the icon in the taskbar": "タスクバーのアイコンを非表示にする", "Please login to Kimi Web": "<PERSON><PERSON> Webにログインしてください", "Please login to ChatGLM Web": "ChatGLM Webにログインしてください", "Free": "無料", "to get your refresh_token.": "あなたのrefresh_tokenを取得する。", "to get your access_token.": "あなたのaccess_tokenを取得する。", "to get your token.": "あなたのtokenを取得する。", "to get the solutions.": "ソリューションを取得する。", "ChatGLM": "ChatGLM", "Font size": "フォントサイズ", "Font family": "フォントファミリー", "UI font size": "UIフォントサイズ", "UI font family": "UIフォントファミリー", "Icon size": "アイコンサイズ", "Window background blur": "ウィンドウ背景のぼかし", "For Windows 11 only. If the window background blur effect is enabled, please ensure to set the 'Theme' to 'Follow the System', as it is currently not possible to manually switch between light and dark themes when the window background blur is active.": "Windows 11専用です。ウィンドウの背景ぼかし効果が有効になっている場合は、「テーマ」を「システムに従う」に設定してください。ウィンドウの背景ぼかしがアクティブな場合、手動でライトテーマとダークテーマを切り替えることはできません。", "If the window background blur effect is enabled, please ensure to set the 'Theme' to 'Follow the System', as it is currently not possible to manually switch between light and dark themes when the window background blur is active.": "ウィンドウの背景ぼかし効果が有効になっている場合は、「テーマ」を「システムに合わせる」に設定してください。ウィンドウの背景ぼかしがアクティブな場合、手動でライトテーマとダークテーマを切り替えることはできません。", "Seconds": "秒", "Minutes": "分", "Hours": "時間", "Days": "日", "Forever": "永遠", "The survival time of the Ollama model in memory": "Ollamaモデルのメモリ内の生存時間", "No models API support": "モデルAPIサポートなし", "Some providers claiming to be compatible with OpenAI's API do not actually support OpenAI's standard model API. Therefore, we have no choice but to offer this option. If you choose this option (and then need to click the save button), we will not attempt to dynamically fetch the latest model list from the model API, but will only use a fixed model list and custom models.": "OpenAIのAPIと互換性があると主張している一部のプロバイダーは、実際にはOpenAIの標準モデルAPIをサポートしていません。そのため、このオプションを提供する以外に選択肢はありません。このオプションを選択する場合（その後保存ボタンをクリックする必要があります）、最新のモデルリストをモデルAPIから動的に取得しようとはしませんが、固定のモデルリストとカスタムモデルのみを使用します。", "If you find this tool helpful, you can buy me a cup of coffee.": "このツールが役に立ったら、コーヒーを買ってください。"}