{"Translate": "Translate", "Summarize": "Summarize", "Polishing": "Polishing", "Analyze": "Analyze", "Explain Code": "Explain Code", "Nop": "Nop", "Follow the System": "Follow the System", "Dark": "Dark", "Light": "Light", "Clear input": "Clear input", "Copy to clipboard": "Copy to clipboard", "Speak": "Speak", "Upload an image for OCR translation": "Upload an image for OCR translation", "Default service provider": "Default service provider", "Default translate mode": "Default translate mode", "Auto Translate": "Auto Translate", "Word selection in input": "Enable word selection for lookup in the input field", "Read the selected words in input": "Read word selection from the input field automatically", "Always show icons": "Always show icons", "Default target language": "Default target language", "Theme": "Theme", "i18n": "Language", "Hotkey": "<PERSON><PERSON>", "OCR Hotkey": "OCR Hotkey", "Writing Hotkey": "Writing Hotkey", "Please press the hotkey you want to set.": "Please press the hotkey you want to set.", "Click above to set hotkeys.": "Click above to set hotkeys.", "Save": "Save", "Saved": "Saved", "Go to Translator": "Go to Translator", "Go to Settings": "Go to Settings", "API Key": "API Key", "API URL": "API URL", "API URL Path": "API URL Path", "is required": "is required", "Go to the": "Go to the", "OpenAI page": "OpenAI page", "MiniMax page": "MiniMax page", "to get your API Key. You can separate multiple API Keys with English commas to achieve quota doubling and load balancing.": "to get your API Key. You can separate multiple API Keys with English commas to achieve quota doubling and load balancing.", "to get your API Key.": "to get your API Key.", "to get your Group ID.": "to get your Group ID.", "Restore Previous Position": "Restore Previous Position", "Fixed Position": "Fixed Position", "Run at startup": "Run at startup", "API Model": "API Model", "Chars Limited": "The number of characters exceeds the limit", "Country Not Supported": "You are directly connecting to OpenAI from {{name}}, which is not a <0>supported region</0> of OpenAI. Continuing to use without extra network configuration may result in your account being blocked by OpenAI, regardless of your ChatGPT Plus subscription status or any remaining account balance.", "Country Not Detected": "We were unable to check if your IP address is in a <0>supported region</0> of OpenAI. Please check your Internet connection, and ensure that you are accessing the API in a supported region of OpenAI, or your account may get banned regardless of your GPT Plus subscription status or any remaining account balance.", "words are collected": "{{collectTotal}} words are collected", "Collection Review": "Collection Review", "Export your collection as a csv file": "Export your collection as a csv file", "CSV file saved on Desktop": "CSV file saved on Desktop", "Remove from collection": "Remove from collection", "Add to collection": "Add to collection", "Auto collect": "Collect automatically", "Random Change": "Random Change", "Please select an essay type": "Please select an essay type", "is writing": "is writing", "An insteresting story": "An insteresting story", "A political newsletter": "A political newsletter", "A sports bulletin": "A sports bulletin", "A catchy lyric": "A catchy lyric", "A smooth poem": "A smooth poem", "No article type selected": "No article type selected", "review count": "Review Count", "last review": "Last Review Time", "vocabulary": "Vocabulary", "generate article": "Generate article", "insert to editor": "Insert to Editor", "Show button when text is selected": "Show button when text is selected", "Show icon when text is selected": "Show icon when text is selected", "Using clipboard": "Using clipboard", "Allow using the clipboard to get the selected text when the selected text is not available": "Allow using the clipboard to get the selected text when the selected text is not available", "Please login to ChatGPT Web": "Please login to ChatGPT Web", "It is highly recommended to disable this feature and use the Clip Extension": "It is highly recommended to disable this feature and use the Clip Extension", "Clip Extension": "Clip Extension", "built-in": "built-in", "Default Action": "Default Action", "Created at": "Created at", "Updated at": "Updated at", "Name": "Name", "Icon": "Icon", "Description": "Description", "Placeholders": "Placeholders", "Role Prompt": "Role Prompt", "Role prompt indicates what role the action represents.": "Role prompt indicates what role the action represents.", "Role prompt example: You are a translator.": "Role prompt example: You are a translator.", "Command Prompt": "Command Prompt", "Command prompt indicates what command should be issued to the role represented by the action when the action is executed.": "Command prompt indicates what command should be issued to the role represented by the action when the action is executed.", "Command prompt example: Please translate the following text from ${sourceLang} to ${targetLang}.": "Command prompt example: Please translate the following text from ${sourceLang} to ${targetLang}.", "Create": "Create", "Delete": "Delete", "Update": "Update", "Action": "Action", "Action Manager": "Action Manager", "Update sth": "Update {{0}}", "Create sth": "Create {{0}}", "Delete sth": "Delete {{0}}", "Are you sure to delete sth?": "Are you sure to delete {{0}}?", "Ok": "Ok", "Cancel": "Cancel", "Submit": "Submit", "represents the source language": "represents the source language", "represents the target language": "represents the target language", "represents the original text, which is usually not needed inside the prompt because it is automatically injected": "represents the original text, which is usually not needed inside the prompt because it is automatically injected", "Output rendering format": "Output rendering format", "disable collecting statistics": "Disable collecting statistics", "Buy me a coffee": "Buy me a coffee", "Writing target language": "Writing target language", "Press this shortcut key in the input box of any application, and the text already entered in the input box will be automatically translated into the writing target language.": "Press this shortcut key in the input box of any application, and the text already entered in the input box will be automatically translated into the writing target language.", "Writing line break shortcut": "Writing line break shortcut", "When writing, which key should be pressed when encountering a line break?": "When writing, which key should be pressed when encountering a line break?", "It is recommended to download the desktop application of OpenAI Translator to enjoy the wonderful experience of word translation in all software!": "It is recommended to download the desktop application of OpenAI Translator to enjoy the wonderful experience of word translation in all software!", "Download Link": "Download Link", "General": "General", "TTS": "TTS", "Writing": "Writing", "Shortcuts": "Shortcuts", "Advanced": "Advanced", "About": "About", "Provider": "Provider", "Rate": "Rate", "Volume": "Volume", "Voice": "Voice", "Slow": "Slow", "Fast": "Fast", "Quiet": "Quiet", "Loud": "Loud", "Add": "Add", "Go back": "Go back", "Updater": "Updater", "Checking for the latest version ...": "Checking for the latest version ...", "Congratulations! You are now using the latest version!": "Congratulations! You are now using the latest version!", "A new version is available!": "A new version is available!", "The current version is {{0}}, and the latest version is": "The current version is {{0}}, and the latest version is", "The release content:": "The release content:", "The update content:": "The update content:", "Close": "Close", "Automatic check for updates": "Automatic check for updates", "Enable mica (Experimental)": "Experimental mica (Experimental)", "**Win11 only. If the mica effect is enabled, it is essential to set the `Theme` to `Follow the system`, as it is currently not possible to manually switch between the light and dark themes of mica.": "**Win11 only. If the mica effect is enabled, it is essential to set the `Theme` to `Follow the system`, as it is currently not possible to manually switch between the light and dark themes of mica.", "Display window Hotkey": "Display window Hotkey", "Hide the icon in the Dock bar": "Hide the icon in the Dock bar", "Disagree": "Disagree", "Agree and continue": "Agree and continue", "Disclaimer": "Disclaimer", "How to Use": "How to Use", "Please select a language": "Please select a language", "Please select a voice": "Please select a voice", "Language detection engine": "Language detection engine", "Google": "Google", "Baidu": "Baidu", "Bing": "<PERSON>", "Auto hide window when out of focus": "Auto hide window when out of focus", "Proxy": "Proxy", "Enabled": "Enabled", "Protocol": "Protocol", "Server": "Server", "Port": "Port", "Username": "Username", "Password": "Password", "No proxy": "No proxy", "Test proxy": "Test proxy", "Test": "Test", "Result": "Result", "Your proxy is working fine": "Your proxy is working fine", "Your proxy is not working": "Your proxy is not working", "Location": "Location", "Time consumed": "Time consumed", "Custom": "Custom", "Custom Model Name": "Custom Model Name", "Local": "Local", "Local Model": "Local Model", "to learn how to install and setup.": "to learn how to install and setup.", "Model needs to first use the `ollama pull` command to download locally, please view all models from this page:": "Model needs to first use the `ollama pull` command to download locally, please view all models from this page:", "Generally, there is no need to modify this item.": "Generally, there is no need to modify this item.", "Hide the icon in the taskbar": "Hide the icon in the taskbar", "Please login to Kimi Web": "Please login to <PERSON>i Web", "Please login to ChatGLM Web": "Please login to ChatGLM Web", "Free": "Free", "to get your refresh_token.": "to get your refresh_token.", "to get your access_token.": "to get your access_token.", "to get your token.": "to get your token.", "to get the solutions.": "to get the solutions.", "ChatGLM": "ChatGLM", "Font size": "Font size", "Font family": "<PERSON>ont family", "UI font size": "UI font size", "UI font family": "UI font family", "Icon size": "Icon size", "Window background blur": "Window background blur", "For Windows 11 only. If the window background blur effect is enabled, please ensure to set the 'Theme' to 'Follow the System', as it is currently not possible to manually switch between light and dark themes when the window background blur is active.": "For Windows 11 only. If the window background blur effect is enabled, please ensure to set the 'Theme' to 'Follow the System', as it is currently not possible to manually switch between light and dark themes when the window background blur is active.", "If the window background blur effect is enabled, please ensure to set the 'Theme' to 'Follow the System', as it is currently not possible to manually switch between light and dark themes when the window background blur is active.": "If the window background blur effect is enabled, please ensure to set the 'Theme' to 'Follow the System', as it is currently not possible to manually switch between light and dark themes when the window background blur is active.", "Seconds": "Seconds", "Minutes": "Minutes", "Hours": "Hours", "Days": "Days", "Forever": "Forever", "The survival time of the Ollama model in memory": "The survival time of the Ollama model in memory", "No models API support": "No models API support", "Some providers claiming to be compatible with OpenAI's API do not actually support OpenAI's standard model API. Therefore, we have no choice but to offer this option. If you choose this option (and then need to click the save button), we will not attempt to dynamically fetch the latest model list from the model API, but will only use a fixed model list and custom models.": "Some providers claiming to be compatible with OpenAI's API do not actually support OpenAI's standard model API. Therefore, we have no choice but to offer this option. If you choose this option (and then need to click the save button), we will not attempt to dynamically fetch the latest model list from the model API, but will only use a fixed model list and custom models.", "If you find this tool helpful, you can buy me a cup of coffee.": "If you find this tool helpful, you can buy me a cup of coffee."}