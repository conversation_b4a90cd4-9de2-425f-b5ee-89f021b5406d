{"Translate": "<PERSON><PERSON><PERSON>", "Summarize": "Özetle", "Polishing": "<PERSON><PERSON><PERSON><PERSON>", "Analyze": "<PERSON><PERSON><PERSON>", "Explain Code": "Kodu Açıkla", "Nop": "İşlem Yok", "Follow the System": "<PERSON><PERSON>mi <PERSON>", "Dark": "Karanlık", "Light": "Aydınlık", "Clear input": "<PERSON><PERSON><PERSON><PERSON>", "Copy to clipboard": "<PERSON><PERSON>", "Speak": "Konuş", "Upload an image for OCR translation": "OCR çevirisi için bir resim y<PERSON>", "Default service provider": "Varsayılan hizmet sağlayıcı", "Default translate mode": "Varsayılan çeviri modu", "Auto Translate": "Otomatik Çeviri", "Word selection in input": "<PERSON><PERSON><PERSON> kelime se<PERSON>", "Read the selected words in input": "<PERSON><PERSON>ş alanındaki seçili kelimeleri otomatik oku", "Always show icons": "Her zaman simgeleri g<PERSON>ster", "Default target language": "Varsayılan hedef dil", "Theme": "<PERSON><PERSON>", "i18n": "Dil", "Hotkey": "Kısayol Tuşu", "OCR Hotkey": "OCR Kısayol Tuşu", "Writing Hotkey": "Yazma Kısayol Tuşu", "Please press the hotkey you want to set.": "Lütfen ayarlamak istediğiniz kısayol tuşuna basın.", "Click above to set hotkeys.": "Kısayol tuşlarını ayarlamak için yukarıya tıklayın.", "Save": "<PERSON><PERSON>", "Saved": "<PERSON><PERSON><PERSON><PERSON>", "Go to Translator": "Çeviriye Git", "Go to Settings": "<PERSON><PERSON><PERSON><PERSON>", "API Key": "API Anahtarı", "API URL": "API URL'si", "API URL Path": "API URL Yolu", "is required": "gereklidir", "Go to the": "Şuraya git:", "OpenAI page": "OpenAI sayfası", "MiniMax page": "MiniMax sayfası", "to get your API Key. You can separate multiple API Keys with English commas to achieve quota doubling and load balancing.": "API Anahtarınızı almak için. Kota ikiye katlamak ve yük dengelemesi için birden fazla API Anahtarını İngilizce virgüllerle ayırabilirsiniz.", "to get your API Key.": "API Anahtarınızı almak için.", "to get your Group ID.": "Grup Kimliğinizi almak için.", "Restore Previous Position": "Önceki <PERSON>", "Fixed Position": "Sabit <PERSON>num", "Run at startup": "Başlangıçta çalıştır", "API Model": "API Modeli", "Chars Limited": "<PERSON><PERSON>er sınırı aşıldı", "Country Not Supported": "{{name}} üzerinden OpenAI'ya doğrudan bağlanıyorsunuz, bu OpenAI'nın <0>desteklenen bölgelerinden</0> biri değil. Ek ağ yapılandırması olmadan kullanmaya devam etmek, ChatGPT Plus abonelik durumunuzdan veya kalan hesap bakiyenizden bağımsız olarak hesabınızın OpenAI tarafından engellenmesine neden olabilir.", "Country Not Detected": "IP adresinizin OpenAI'nın <0>desteklenen bölgelerinden</0> birinde olup olmadığını kontrol edemedik. Lütfen İnternet bağlantınızı kontrol edin ve API'ye OpenAI'nın desteklenen bir bölgesinden eriştiğinizden emin olun, aksi takdirde GPT Plus abonelik durumunuzdan veya kalan hesap bakiyenizden bağımsız olarak hesabınız yasaklanabilir.", "words are collected": "{{collectTotal}} kelime toplandı", "Collection Review": "Koleksiyon İncelemesi", "Export your collection as a csv file": "Koleksiyonunuzu csv dosyası olarak dışa aktarın", "CSV file saved on Desktop": "CSV dosyası Masaüstüne kaydedildi", "Remove from collection": "Koleksi<PERSON><PERSON> kaldır", "Add to collection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Auto collect": "Otomatik topla", "Random Change": "<PERSON><PERSON><PERSON><PERSON>", "Please select an essay type": "Lütfen bir kompozisyon türü se<PERSON>in", "is writing": "<PERSON><PERSON>ı<PERSON>r", "An insteresting story": "İlginç bir hikaye", "A political newsletter": "<PERSON><PERSON><PERSON> bir b<PERSON>lten", "A sports bulletin": "Spor bülteni", "A catchy lyric": "<PERSON><PERSON><PERSON>lda kalıcı bir şarkı sözü", "A smooth poem": "Akıcı bir şiir", "No article type selected": "<PERSON><PERSON><PERSON> türü seçilmedi", "review count": "İnceleme <PERSON>", "last review": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "vocabulary": "<PERSON><PERSON><PERSON>", "generate article": "Makale oluştur", "insert to editor": "<PERSON><PERSON><PERSON>", "Show button when text is selected": "<PERSON><PERSON>ğinde düğmeyi göster", "Show icon when text is selected": "<PERSON><PERSON>ğinde simgeyi göster", "Using clipboard": "<PERSON><PERSON><PERSON> k<PERSON>", "Allow using the clipboard to get the selected text when the selected text is not available": "Seçili metin mevcut olmadığında seçili metni almak için panoyu kullanmaya izin ver", "Please login to ChatGPT Web": "Lütfen ChatGPT Web'e giriş yapın", "It is highly recommended to disable this feature and use the Clip Extension": "Bu özelliği devre dışı bırakmanız ve Clip Uzantısını kullanmanız şiddetle tavsiye edilir", "Clip Extension": "<PERSON><PERSON>", "built-in": "<PERSON><PERSON>ş<PERSON>", "Default Action": "Varsayılan Eylem", "Created at": "Oluşturulma zamanı", "Updated at": "G<PERSON><PERSON><PERSON><PERSON> z<PERSON>ı", "Name": "İsim", "Icon": "<PERSON>m<PERSON>", "Description": "<PERSON><PERSON>ı<PERSON><PERSON>", "Placeholders": "Yer tutucular", "Role Prompt": "Rol İstemi", "Role prompt indicates what role the action represents.": "<PERSON><PERSON> istem<PERSON>, eylemin hangi rolü temsil ettiğini belirtir.", "Role prompt example: You are a translator.": "<PERSON>ol istemi örneği: <PERSON>.", "Command Prompt": "<PERSON><PERSON><PERSON>", "Command prompt indicates what command should be issued to the role represented by the action when the action is executed.": "<PERSON><PERSON><PERSON>, e<PERSON><PERSON>ü<PERSON>tüldüğünde eylemin temsil ettiği role hangi komutun verilmesi gerektiğini belirtir.", "Command prompt example: Please translate the following text from ${sourceLang} to ${targetLang}.": "Komut istemi ö<PERSON>ği: Lütfen aşağıdaki metni ${sourceLang} dilinden ${targetLang} diline çevirin.", "Create": "Oluştur", "Delete": "Sil", "Update": "<PERSON><PERSON><PERSON><PERSON>", "Action": "<PERSON><PERSON><PERSON>", "Action Manager": "<PERSON><PERSON><PERSON>", "Update sth": "{{0}} <PERSON><PERSON><PERSON><PERSON>", "Create sth": "{{0}} Oluştur", "Delete sth": "{{0}} Sil", "Are you sure to delete sth?": "{{0}} öğesini silmek istediğinizden emin misiniz?", "Ok": "<PERSON><PERSON>", "Cancel": "İptal", "Submit": "<PERSON><PERSON><PERSON>", "represents the source language": "kaynak dili temsil eder", "represents the target language": "hedef dili temsil eder", "represents the original text, which is usually not needed inside the prompt because it is automatically injected": "orijinal metni te<PERSON>il eder, genellikle istemde gerekli <PERSON>ğ<PERSON>dir çünkü otomatik olarak enjekte edilir", "Output rendering format": "Çıktı işleme formatı", "disable collecting statistics": "İstatistik toplamayı devre dışı bırak", "Buy me a coffee": "<PERSON>a bir kahve <PERSON>a", "Writing target language": "<PERSON><PERSON><PERSON>", "Press this shortcut key in the input box of any application, and the text already entered in the input box will be automatically translated into the writing target language.": "Herhangi bir uygulamanın giriş kutusunda bu kısayol tuşuna basın ve giriş kutusuna zaten girilmiş olan metin otomatik olarak yazma hedef diline çevrilecektir.", "Writing line break shortcut": "<PERSON><PERSON><PERSON><PERSON> sonu kısayolu yazma", "When writing, which key should be pressed when encountering a line break?": "<PERSON><PERSON><PERSON>, bir satır sonu ile ka<PERSON>ıldığında hangi tuşa basılmalıdır?", "It is recommended to download the desktop application of OpenAI Translator to enjoy the wonderful experience of word translation in all software!": "Tüm yazıl<PERSON><PERSON><PERSON>a kelime çeviri<PERSON>in harika deneyimini yaşamak için OpenAI Translator'ın masaüstü uygulamasını indirmeniz önerilir!", "Download Link": "İndirme Bağlantısı", "General": "<PERSON><PERSON>", "TTS": "TTS", "Writing": "<PERSON><PERSON><PERSON>", "Shortcuts": "K<PERSON><PERSON>ollar", "Advanced": "Gelişmiş", "About": "Hakkında", "Provider": "Sağlayıcı", "Rate": "<PERSON><PERSON>", "Volume": "<PERSON><PERSON>", "Voice": "Ses", "Slow": "Yavaş", "Fast": "Hızlı", "Quiet": "<PERSON><PERSON><PERSON>", "Loud": "<PERSON><PERSON><PERSON><PERSON>", "Add": "<PERSON><PERSON>", "Go back": "<PERSON><PERSON>", "Updater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Checking for the latest version ...": "En son sü<PERSON><PERSON><PERSON> kontrol ediliyor ...", "Congratulations! You are now using the latest version!": "Tebrikler! <PERSON>u anda en son sürü<PERSON>ü kullanıyorsunuz!", "A new version is available!": "Yeni bir sürüm mevcut!", "The current version is {{0}}, and the latest version is": "Mevcut sürüm {{0}}, en son sürüm ise", "The release content:": "S<PERSON>rüm içeriği:", "The update content:": "<PERSON><PERSON><PERSON><PERSON><PERSON>:", "Close": "Ka<PERSON><PERSON>", "Automatic check for updates": "Otomatik güncelleme kontrolü", "Enable mica (Experimental)": "Mi<PERSON>'<PERSON><PERSON>tir (Deneysel)", "**Win11 only. If the mica effect is enabled, it is essential to set the `Theme` to `Follow the system`, as it is currently not possible to manually switch between the light and dark themes of mica.": "**Yalnızca Win11. Mica efekti etkinleştirilirse, '<PERSON><PERSON><PERSON><PERSON><PERSON> '<PERSON><PERSON><PERSON> ta<PERSON> et' olarak ayarlamak önemlidir, <PERSON>ü<PERSON><PERSON> <PERSON>u anda mica'nın açık ve koyu temaları arasında manuel olarak geçiş yapmak mümkün değildir.", "Display window Hotkey": "<PERSON><PERSON><PERSON> Görüntüleme Kısayol Tuşu", "Hide the icon in the Dock bar": "Dock çubuğundaki simgeyi gizle", "Disagree": "Katılmıyorum", "Agree and continue": "Kabul et ve devam et", "Disclaimer": "Feragatname", "How to Use": "<PERSON><PERSON><PERSON><PERSON>", "Please select a language": "Lütfen bir dil seçin", "Please select a voice": "Lütfen bir ses seçin", "Language detection engine": "<PERSON>l algılama motoru", "Google": "Google", "Baidu": "Baidu", "Bing": "<PERSON>", "Auto hide window when out of focus": "Odak dışındayken pencereyi otomatik gizle", "Proxy": "Proxy", "Enabled": "<PERSON><PERSON><PERSON>", "Protocol": "Protokol", "Server": "<PERSON><PERSON><PERSON>", "Port": "Port", "Username": "Kullanıcı adı", "Password": "Şifre", "No proxy": "Proxy yok", "Test proxy": "Proxy'yi test et", "Test": "Test", "Result": "<PERSON><PERSON><PERSON>", "Your proxy is working fine": "Proxy'niz d<PERSON><PERSON><PERSON><PERSON><PERSON>alışıyor", "Your proxy is not working": "Proxy'niz ç<PERSON>ışmıyor", "Location": "<PERSON><PERSON>", "Time consumed": "<PERSON><PERSON><PERSON> s<PERSON>re", "Custom": "<PERSON><PERSON>", "Custom Model Name": "Özel Model Adı", "Local": "<PERSON><PERSON>", "Local Model": "<PERSON><PERSON>", "to learn how to install and setup.": "nasıl kurulup ayarlanacağını öğrenmek için.", "Model needs to first use the `ollama pull` command to download locally, please view all models from this page:": "Model önce `o<PERSON><PERSON> pull` komutunu kullanarak yerel olarak indirilmelidir, lütfen tüm modelleri bu <PERSON><PERSON><PERSON> g<PERSON>:", "Generally, there is no need to modify this item.": "Genellikle bu öğ<PERSON><PERSON>ğiştirmeye gerek yoktur.", "Hide the icon in the taskbar": "Görev çubuğundaki simgeyi gizle", "Please login to Kimi Web": "Lütfen Kimi Web'e giriş yapın", "Please login to ChatGLM Web": "Lütfen ChatGLM Web'e giriş yapın", "Free": "Ücretsiz", "to get your refresh_token.": "refresh_token'ınızı almak için.", "to get your access_token.": "access_token'ınızı almak için.", "to get your token.": "token'ınızı almak için.", "to get the solutions.": "çözümleri almak için.", "ChatGLM": "ChatGLM", "Font size": "Ya<PERSON><PERSON> tipi boyutu", "Font family": "Yazı tipi ailesi", "UI font size": "Arayüz yazı tipi boyutu", "UI font family": "Arayüz yazı tipi ailesi", "Icon size": "<PERSON><PERSON><PERSON>", "Window background blur": "Pencere arka plan bulanıklığı", "For Windows 11 only. If the window background blur effect is enabled, please ensure to set the 'Theme' to 'Follow the System', as it is currently not possible to manually switch between light and dark themes when the window background blur is active.": "Yalnızca Windows 11 için. Pencere arka plan bulanıklık efekti etkinleştirilirse, lütfen 'Tema'yı 'Sistemi Takip Et' olarak ayarladığınızdan emin olu<PERSON>, çünkü pencere arka plan bulanıklığı etkinken açık ve koyu temalar arasında manuel olarak geçiş yapmak şu anda mümkün değildir.", "If the window background blur effect is enabled, please ensure to set the 'Theme' to 'Follow the System', as it is currently not possible to manually switch between light and dark themes when the window background blur is active.": "Pencere arka plan bulanıklık efekti etkinleştirilirse, lütfen 'Tema'y<PERSON> 'Sistemi Takip Et' olarak ayarladığınızdan emin olun, çünkü pencere arka plan bulanıklığı etkinken açık ve koyu temalar arasında manuel olarak geçiş yapmak şu anda mümkün değildir.", "Seconds": "<PERSON><PERSON><PERSON>", "Minutes": "Dakika", "Hours": "Saat", "Days": "<PERSON><PERSON><PERSON>", "Forever": "<PERSON><PERSON><PERSON><PERSON>", "The survival time of the Ollama model in memory": "Ollama modelinin bellekteki ya<PERSON>", "No models API support": "Model API desteği yok", "Some providers claiming to be compatible with OpenAI's API do not actually support OpenAI's standard model API. Therefore, we have no choice but to offer this option. If you choose this option (and then need to click the save button), we will not attempt to dynamically fetch the latest model list from the model API, but will only use a fixed model list and custom models.": "OpenAI'nin API'si ile uyumlu olduğunu iddia eden bazı sağlayıcılar, aslında OpenAI'nin standart model API'sini desteklemiyor. Bu ne<PERSON>, bu seçeneği sunmaktan başka çaremiz yok. Bu seçeneği seçerseniz (ve ardından kaydet düğmesine tıklamanız gerekir), model API'sinden en son model listesini dinamik olarak almaya çalışmayacağız, sadece sabit bir model listesi ve özel modeller kullanacağız.", "If you find this tool helpful, you can buy me a cup of coffee.": "Bu aracı faydalı bulu<PERSON>nız, bana bir fincan kahve ısmarlayabilirsiniz."}