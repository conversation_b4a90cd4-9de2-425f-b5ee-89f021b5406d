<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>Actions</key>
        <array>
            <dict>
                <key>Shell Script File</key>
                <string>openai-translator.sh</string>
                <key>Image File</key>
                <string>icon.png</string>
                <key>Title</key>
                <string>OpenAI Translator</string>
            </dict>
        </array>
        <key>Credits</key>
        <array>
            <dict>
                <key>Link</key>
                <string>https://github.com/openai-translator/openai-translator</string>
                <key>Name</key>
                <string>OpenAI Translator</string>
            </dict>
        </array>
        <key>Extension Description</key>
        <string>Translate text with OpenAI Translator.</string>
        <key>Extension Identifier</key>
        <string>xyz.yetone.apps.openai-translator.clip-extensions.popclip</string>
        <key>Extension Image File</key>
        <string>openai-translator.png</string>
        <key>Extension Name</key>
        <string>OpenAI Translator</string>
        <key>Required OS Version</key>
        <string>10.13</string>
    </dict>
</plist>
